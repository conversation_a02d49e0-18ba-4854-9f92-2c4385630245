use burn::{
    data::{
        dataloader::batcher::Bat<PERSON>,
        dataset::vision::MnistItem,
        dataset::{Dataset, InMemDataset},
    },
    prelude::*,
};
use ndarray::{Array3, s};
use serde::{Deserialize, Serialize};

const NUM_SYMBOLS: usize = 8;
const NUM_FACTORS: usize = 251;

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct MnistBatcher {}

#[derive(<PERSON><PERSON>, Debug)]
pub struct MnistBatch<B: Backend> {
    pub images: Tensor<B, 3>,
    pub targets: Tensor<B, 1, Int>,
}

impl<B: Backend> Batcher<B, MnistItem, MnistBatch<B>> for MnistBatcher {
    fn batch(&self, items: Vec<MnistItem>, device: &B::Device) -> MnistBatch<B> {
        let images = items
            .iter()
            .map(|item| TensorData::from(item.image).convert::<B::FloatElem>())
            .map(|data| Tensor::<B, 2>::from_data(data, device))
            .map(|tensor| tensor.reshape([1, 28, 28]))
            // Normalize: scale between [0,1] and make the mean=0 and std=1
            // values mean=0.1307,std=0.3081 are from the PyTorch MNIST example
            // https://github.com/pytorch/examples/blob/54f4572509891883a947411fd7239237dd2a39c3/mnist/main.py#L122
            .map(|tensor| ((tensor / 255) - 0.1307) / 0.3081)
            .collect();

        let targets = items
            .iter()
            .map(|item| {
                Tensor::<B, 1, Int>::from_data([(item.label as i64).elem::<B::IntElem>()], device)
            })
            .collect();

        let images = Tensor::cat(images, 0);
        let targets = Tensor::cat(targets, 0);

        MnistBatch { images, targets }
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct FactorDataItem {
    pub features: Vec<f32>,  // 特征数据 (251维)
    pub labels: Vec<f32>,    // 标签数据 (3维)
    pub symbol_id: usize,    // 符号ID (0-7)
    pub timestamp_id: usize, // 时间戳ID
}

#[derive(Clone, Debug)]
pub struct FactorBatch<B: Backend> {
    pub features: Tensor<B, 2>, // [batch_size, num_symbols, num_features]
    pub labels: Tensor<B, 2>,   // [batch_size, num_symbols, num_lables]
    pub symbol_ids: Tensor<B, 1, Int>, // [batch_size]
}

#[derive(Clone, Default)]
pub struct FactorBatcher {}

impl<B: Backend> Batcher<B, FactorDataItem, FactorBatch<B>> for FactorBatcher {
    fn batch(&self, items: Vec<FactorDataItem>, device: &B::Device) -> FactorBatch<B> {
        let batch_size = items.len();

        // 收集特征数据
        let features: Vec<Tensor<B, 1>> = items
            .iter()
            .map(|item| {
                let data = TensorData::from(item.features.as_slice()).convert::<B::FloatElem>();
                Tensor::<B, 1>::from_data(data, device)
            })
            .collect();

        // 收集标签数据
        let labels: Vec<Tensor<B, 1>> = items
            .iter()
            .map(|item| {
                let data = TensorData::from(item.labels.as_slice()).convert::<B::FloatElem>();
                Tensor::<B, 1>::from_data(data, device)
            })
            .collect();

        // 收集符号ID
        let symbol_ids: Vec<Tensor<B, 1, Int>> = items
            .iter()
            .map(|item| {
                Tensor::<B, 1, Int>::from_data(
                    [(item.symbol_id as i64).elem::<B::IntElem>()],
                    device,
                )
            })
            .collect();

        let features = Tensor::cat(features, 0).reshape([batch_size, NUM_FACTORS]);
        let labels = Tensor::cat(labels, 0).reshape([batch_size, 3]); 
        let symbol_ids = Tensor::cat(symbol_ids, 0);

        FactorBatch {
            features,
            labels,
            symbol_ids,
        }
    }
}

pub struct FactorDataset {
    dataset: InMemDataset<FactorDataItem>,
}

impl Dataset<FactorDataItem> for FactorDataset {
    fn get(&self, index: usize) -> Option<FactorDataItem> {
        self.dataset.get(index)
    }

    fn len(&self) -> usize {
        self.dataset.len()
    }
}

impl FactorDataset {
    /// 从 Array3<f32> 创建数据集
    /// raw_data shape: [254, 835200, 8]
    /// 其中前251维是特征，后3维是标签
    pub fn from_array3(raw_data: Array3<f32>) -> Self {
        let (num_features, num_timestamps, num_symbols) = raw_data.dim();
        assert_eq!(num_features, 254, "Expected 254 features (251 + 3 labels)");
        assert_eq!(num_symbols, NUM_SYMBOLS, "Expected {} symbols", NUM_SYMBOLS);

        let mut items = Vec::new();

        // 遍历每个符号和时间戳
        for symbol_id in 0..num_symbols {
            for timestamp_id in 0..num_timestamps {
                // 提取特征 (前251维)
                let features: Vec<f32> = (0..NUM_FACTORS)
                    .map(|feature_idx| raw_data[[feature_idx, timestamp_id, symbol_id]])
                    .collect();

                // 提取标签 (后3维)
                let labels: Vec<f32> = (NUM_FACTORS..num_features)
                    .map(|feature_idx| raw_data[[feature_idx, timestamp_id, symbol_id]])
                    .collect();

                let item = FactorDataItem {
                    features,
                    labels,
                    symbol_id,
                    timestamp_id,
                };

                items.push(item);
            }
        }

        let dataset = InMemDataset::new(items);
        Self { dataset }
    }

    /// 创建训练集 (可以根据需要分割数据)
    pub fn train(raw_data: Array3<f32>) -> Self {
        // 这里可以实现训练集的逻辑，比如取前80%的时间戳
        Self::from_array3(raw_data)
    }

    /// 创建测试集 (可以根据需要分割数据)
    pub fn test(raw_data: Array3<f32>) -> Self {
        // 这里可以实现测试集的逻辑，比如取后20%的时间戳
        Self::from_array3(raw_data)
    }

    /// 按时间分割数据集
    pub fn split_by_time(raw_data: Array3<f32>, train_ratio: f32) -> (Self, Self) {
        let (_num_features, num_timestamps, _num_symbols) = raw_data.dim();
        let split_point = (num_timestamps as f32 * train_ratio) as usize;

        // 训练集：前 split_point 个时间戳
        let train_data = raw_data.slice(s![.., ..split_point, ..]).to_owned();
        let train_dataset = Self::from_array3(train_data);

        // 测试集：后面的时间戳
        let test_data = raw_data.slice(s![.., split_point.., ..]).to_owned();
        let test_dataset = Self::from_array3(test_data);

        (train_dataset, test_dataset)
    }
}

// 使用示例
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_factor_dataset_creation() {
        // 创建示例数据 [254, 100, 8] (较小的数据用于测试)
        let raw_data = Array3::<f32>::zeros((254, 100, 8));

        // 创建数据集
        let dataset = FactorDataset::from_array3(raw_data);

        // 验证数据集大小: 100 timestamps * 8 symbols = 800 items
        assert_eq!(dataset.len(), 800);

        // 获取第一个数据项
        let item = dataset.get(0).unwrap();
        // println!("{:?}", item);
        assert_eq!(item.features.len(), 251);
        assert_eq!(item.labels.len(), 3);
        assert_eq!(item.symbol_id, 0);
        assert_eq!(item.timestamp_id, 0);
    }

    #[test]
    fn test_factor_dataset_split() {
        // 创建示例数据 [254, 50, 8] (较小的数据用于测试)
        let raw_data = Array3::<f32>::ones((254, 50, 8));

        // 按时间分割数据集 (80% 训练, 20% 测试)
        let (train_dataset, test_dataset) = FactorDataset::split_by_time(raw_data, 0.8);

        // 验证分割结果
        // 训练集: 40 timestamps * 8 symbols = 320 items
        assert_eq!(train_dataset.len(), 320);
        // 测试集: 10 timestamps * 8 symbols = 80 items
        assert_eq!(test_dataset.len(), 80);
    }
}
