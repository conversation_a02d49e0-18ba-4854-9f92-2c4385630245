// use crate::{data::M<PERSON><PERSON>atcher, training::TrainingConfig};
// use burn::{
//     data::{dataloader::batcher::<PERSON><PERSON>, dataset::vision::MnistItem},
//     prelude::*,
//     record::{CompactRecorder, Recorder},
// };

// pub fn infer<B: Backend>(artifact_dir: &str, device: B::Device, item: MnistItem) {
//     let config =
//         TrainingConfig::load(format!("{artifact_dir}/config.json")).expect("Failed to load config");
//     let record = CompactRecorder::new()
//         .load(format!("{artifact_dir}/model").into(), &device)
//         .expect("Failed to load model");

//     let model = config.model.init::<B>(&device).load_record(record);

//     let label = item.label;
//     let batcher = MnistBatcher::default();
//     let batch = batcher.batch(vec![item], &device);
//     let output = model.forward(batch.images);
// }
