use crate::{
    data::{<PERSON><PERSON><PERSON>, <PERSON>Batcher, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>atch, MnistBatcher},
    model::{Model, ModelConfig, ModelR, ModelRConfig},
};
use burn::{
    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},
    nn::loss::{CrossEntropyLossConfig, MseLoss, Reduction},
    optim::AdamConfig,
    prelude::*,
    record::CompactRecorder,
    tensor::backend::AutodiffBackend,
    train::{
        ClassificationOutput, LearnerBuilder, RegressionOutput, TrainOutput, TrainStep, ValidStep,
        metric::{AccuracyMetric, LossMetric},
    },
};
use ndarray;
use ndarray_npy;

impl<B: Backend> Model<B> {
    pub fn forward_classification(
        &self,
        images: Tensor<B, 3>,
        targets: Tensor<B, 1, Int>,
    ) -> ClassificationOutput<B> {
        let output = self.forward(images);
        let loss = CrossEntropyLossConfig::new()
            .init(&output.device())
            .forward(output.clone(), targets.clone());

        ClassificationOutput::new(loss, output, targets)
    }
}

impl<B: Backend> ModelR<B> {
    pub fn forward_regression(
        &self,
        x: Tensor<B, 3>,
        targets: Tensor<B, 3>,
    ) -> RegressionOutput<B> {
        let [b, s, _] = x.dims(); // [batch_size, num_symbols, num_features]
        let (primary_pred, all_direct_preds) = self.forward(x);

        let loss_direct =
            MseLoss::new().forward(all_direct_preds.clone(), targets.clone(), Reduction::Auto);

        let primary_y = targets.slice(s![.., .., 0]);
        let loss_primary_final =
            MseLoss::new().forward(primary_pred.clone(), primary_y.clone(), Reduction::Auto);

        let loss = loss_direct + loss_primary_final;

        RegressionOutput::new(
            loss,
            primary_pred.reshape([(b * s) as i32, -1]),
            primary_y.reshape([(b * s) as i32, -1]),
        )
    }
}

impl<B: AutodiffBackend> TrainStep<FactorBatch<B>, RegressionOutput<B>> for ModelR<B> {
    fn step(&self, batch: FactorBatch<B>) -> TrainOutput<RegressionOutput<B>> {
        let item = self.forward_regression(batch.features, batch.labels);

        TrainOutput::new(self, item.loss.backward(), item)
    }
}

impl<B: Backend> ValidStep<FactorBatch<B>, RegressionOutput<B>> for ModelR<B> {
    fn step(&self, batch: FactorBatch<B>) -> RegressionOutput<B> {
        self.forward_regression(batch.features, batch.labels)
    }
}

impl<B: AutodiffBackend> TrainStep<MnistBatch<B>, ClassificationOutput<B>> for Model<B> {
    fn step(&self, batch: MnistBatch<B>) -> TrainOutput<ClassificationOutput<B>> {
        let item = self.forward_classification(batch.images, batch.targets);

        TrainOutput::new(self, item.loss.backward(), item)
    }
}

impl<B: Backend> ValidStep<MnistBatch<B>, ClassificationOutput<B>> for Model<B> {
    fn step(&self, batch: MnistBatch<B>) -> ClassificationOutput<B> {
        self.forward_classification(batch.images, batch.targets)
    }
}

#[derive(Config)]
pub struct TrainingConfig {
    pub model: ModelRConfig,
    pub optimizer: AdamConfig,
    #[config(default = 10)]
    pub num_epochs: usize,
    #[config(default = 64)]
    pub batch_size: usize,
    #[config(default = 4)]
    pub num_workers: usize,
    #[config(default = 42)]
    pub seed: u64,
    #[config(default = 1.0e-4)]
    pub learning_rate: f64,
}

fn create_artifact_dir(artifact_dir: &str) {
    // Remove existing artifacts before to get an accurate learner summary
    std::fs::remove_dir_all(artifact_dir).ok();
    std::fs::create_dir_all(artifact_dir).ok();
}

pub fn train<B: AutodiffBackend>(artifact_dir: &str, config: TrainingConfig, device: B::Device) {
    create_artifact_dir(artifact_dir);
    config
        .save(format!("{artifact_dir}/config.json"))
        .expect("Config should be saved successfully");

    B::seed(config.seed);

    let batcher: FactorBatcher = FactorBatcher::default();

    let data_path = "/home/<USER>/projects/burn_test/data.npy";
    let raw_data = ndarray_npy::read_npy(data_path).unwrap();
    let (train_ds, test_ds) = FactorDataset::split_by_time(raw_data, 0.8);

    let dataloader_train = DataLoaderBuilder::new(batcher.clone())
        .batch_size(config.batch_size)
        .shuffle(config.seed)
        .num_workers(config.num_workers)
        .build(train_ds);

    let dataloader_test = DataLoaderBuilder::new(batcher.clone())
        .batch_size(config.batch_size)
        // .shuffle(config.seed)
        .num_workers(config.num_workers)
        .build(test_ds);

    let learner = LearnerBuilder::new(artifact_dir)
        // .metric_train_numeric(AccuracyMetric::new())
        // .metric_valid_numeric(AccuracyMetric::new())
        .metric_train_numeric(LossMetric::new())
        .metric_valid_numeric(LossMetric::new())
        .with_file_checkpointer(CompactRecorder::new())
        .devices(vec![device.clone()])
        .num_epochs(config.num_epochs)
        .summary()
        .build(
            config.model.init::<B>(&device),
            config.optimizer.init(),
            config.learning_rate,
        );

    let model_trained = learner.fit(dataloader_train, dataloader_test);

    model_trained
        .save_file(format!("{artifact_dir}/model"), &CompactRecorder::new())
        .expect("Trained model should be saved successfully");
}
