mod data;
mod model;
mod training;

use crate::{model::ModelConfig, model::ModelRConfig, training::TrainingConfig};
use burn::{
    backend::{Autodiff, Cuda},
    data::dataset::Dataset,
    optim::AdamConfig,
    prelude::Backend,
    tensor::Tensor,
};

fn main() {
    type MyBackend = Cuda<f32, i32>;
    type MyAutodiffBackend = Autodiff<MyBackend>;

    let device = burn::backend::cuda::CudaDevice::default();
    let artifact_dir = "./tmp/guide";

    crate::training::train::<MyAutodiffBackend>(
        artifact_dir,
        TrainingConfig::new(
            ModelRConfig::new(
                251,
                3,
                vec![256, 128, 64],
                vec![32],
                vec![0.1, 0.1],
                vec![0.1],
            ),
            AdamConfig::new(),
        ),
        device.clone(),
    );
}
