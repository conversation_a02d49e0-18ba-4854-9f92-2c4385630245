use burn::{
    nn::{
        Dropout, DropoutConfig, Linear, LinearConfig, Relu,
        conv::{Conv2d, Conv2dConfig},
        gru::{Gru, GruConfig},
        pool::{AdaptiveAvgPool2d, AdaptiveAvgPool2dConfig},
    },
    prelude::*,
};

#[derive(Module, Debug)]
pub struct Model<B: Backend> {
    conv1: Conv2d<B>,
    conv2: Conv2d<B>,
    pool: AdaptiveAvgPool2d,
    dropout: Dropout,
    linear1: Linear<B>,
    linear2: Linear<B>,
    activation: Relu,
}

#[derive(Config, Debug)]
pub struct ModelConfig {
    num_class: usize,
    hidden_size: usize,

    #[config(default = 0.5)]
    dropout: f64,
}

impl ModelConfig {
    pub fn init<B: Backend>(&self, device: &B::Device) -> Model<B> {
        Model {
            conv1: Conv2dConfig::new([1, 8], [3, 3]).init(device),
            conv2: Conv2dConfig::new([8, 16], [3, 3]).init(device),
            pool: AdaptiveAvgPool2dConfig::new([8, 8]).init(),
            activation: Relu::new(),
            linear1: LinearConfig::new(16 * 8 * 8, self.hidden_size).init(device),
            linear2: LinearConfig::new(self.hidden_size, self.num_class).init(device),
            dropout: DropoutConfig::new(self.dropout).init(),
        }
    }
}

impl<B: Backend> Model<B> {
    // [batch, height, weight] -> [batch, num_classes]
    pub fn forward(&self, images: Tensor<B, 3>) -> Tensor<B, 2> {
        let [batch_size, height, width] = images.dims();

        let x = images.reshape([batch_size, 1, height, width]);

        let x = self.conv1.forward(x);
        let x = self.dropout.forward(x);
        let x = self.conv2.forward(x); // [batch_size, 16, _, _]
        let x = self.dropout.forward(x);
        let x = self.activation.forward(x);

        let x = self.pool.forward(x); // [batch_size, 16, 8, 8]
        let x = x.reshape([batch_size, 16 * 8 * 8]);
        let x = self.linear1.forward(x);
        let x = self.dropout.forward(x);
        let x = self.activation.forward(x);

        self.linear2.forward(x) // [batch_size, num_classes]
    }
}

#[derive(Module, Debug)]
struct RecurrentLayer<B: Backend> {
    gru: Gru<B>,
    dropout: Dropout,
}

#[derive(Module, Debug)]
struct HiddenLinear<B: Backend> {
    linear: Linear<B>,
    dropout: Dropout,
    activation: Relu,
}

#[derive(Module, Debug)]
struct ModelRBase<B: Backend> {
    recurrent_layers: Vec<RecurrentLayer<B>>,
    fc_layers: Vec<HiddenLinear<B>>,
    linear_out: Linear<B>,
}

#[derive(Config, Debug)]
struct ModelRBaseConfig {
    input_size: usize,
    hidden_sizes: Vec<usize>,
    hidden_sizes_linear: Vec<usize>,

    dropouts_recurrent: Vec<f64>,
    dropouts_linear: Vec<f64>,
}

#[derive(Module, Debug)]
pub struct ModelR<B: Backend> {
    base_models: Vec<ModelRBase<B>>,
    linear_out: Linear<B>,
}

#[derive(Config, Debug)]
pub struct ModelRConfig {
    input_size: usize,
    num_labels: usize,
    hidden_sizes: Vec<usize>,
    hidden_sizes_linear: Vec<usize>,

    dropouts_recurrent: Vec<f64>,
    dropouts_linear: Vec<f64>,
}

impl ModelRConfig {
    pub fn init<B: Backend>(&self, device: &B::Device) -> ModelR<B> {
        let num_aux_labels = self.num_labels - 1;
        let base_models: Vec<ModelRBase<B>> = (0..self.num_labels)
            .map(|_| {
                ModelRBaseConfig {
                    input_size: self.input_size,
                    hidden_sizes: self.hidden_sizes.clone(),
                    hidden_sizes_linear: self.hidden_sizes_linear.clone(),
                    dropouts_recurrent: self.dropouts_recurrent.clone(),
                    dropouts_linear: self.dropouts_linear.clone(),
                }
                .init(device)
            })
            .collect();

        let linear_out = LinearConfig::new(num_aux_labels, 1).init(device);

        ModelR {
            base_models,
            linear_out,
        }
    }
}

impl ModelRBaseConfig {
    fn init<B: Backend>(&self, device: &B::Device) -> ModelRBase<B> {
        let mut recurrent_layers: Vec<RecurrentLayer<B>> = vec![RecurrentLayer {
            gru: GruConfig::new(self.input_size, self.hidden_sizes[0], true).init(device),
            dropout: DropoutConfig::new(self.dropouts_recurrent[0]).init(),
        }];

        for i in 0..self.hidden_sizes.len() - 1 {
            let layer: RecurrentLayer<B> = RecurrentLayer {
                gru: GruConfig::new(self.hidden_sizes[i], self.hidden_sizes[i + 1], true)
                    .init(device),
                dropout: DropoutConfig::new(self.dropouts_recurrent[i]).init(),
            };
            recurrent_layers.push(layer);
        }

        let mut fc_layers: Vec<HiddenLinear<B>> = vec![HiddenLinear {
            linear: LinearConfig::new(
                *self.hidden_sizes.last().unwrap(),
                self.hidden_sizes_linear[0],
            )
            .init(device),
            dropout: DropoutConfig::new(self.dropouts_linear[0]).init(),
            activation: Relu::new(),
        }];

        for i in 0..self.hidden_sizes_linear.len() - 1 {
            let layer: HiddenLinear<B> = HiddenLinear {
                linear: LinearConfig::new(
                    self.hidden_sizes_linear[i],
                    self.hidden_sizes_linear[i + 1],
                )
                .init(device),
                dropout: DropoutConfig::new(self.dropouts_linear[i]).init(),
                activation: Relu::new(),
            };
            fc_layers.push(layer);
        }

        let linear_out =
            LinearConfig::new(*self.hidden_sizes_linear.last().unwrap(), 1).init(device);

        ModelRBase {
            recurrent_layers,
            fc_layers,
            linear_out,
        }
    }
}

impl<B: Backend> ModelRBase<B> {
    fn forward(&self, x: Tensor<B, 3>) -> Tensor<B, 3> {
        let [b, s, _] = x.dims(); // [batch_size, num_symbols, num_features]

        let mut recurrent_output: Tensor<B, 3> = x;
        for layer in &self.recurrent_layers {
            recurrent_output = layer.gru.forward(recurrent_output, None);
            recurrent_output = layer.dropout.forward(recurrent_output);
        }

        let mut output = recurrent_output.reshape([(b * s) as i32, -1]);

        for layer in &self.fc_layers {
            output = layer.linear.forward(output);
            output = layer.activation.forward(output);
            output = layer.dropout.forward(output);
        }

        output = self.linear_out.forward(output);
        output.reshape([b, s, 1])
    }
}

impl<B: Backend> ModelR<B> {
    pub fn forward(&self, x: Tensor<B, 3>) -> (Tensor<B, 3>, Tensor<B, 3>) {
        let [b, s, _] = x.dims(); // [batch_size, num_symbols, num_features]

        let mut aux_preds: Vec<Tensor<B, 3>> = self
            .base_models
            .iter()
            .skip(0)
            .map(|m| m.forward(x.clone()))
            .collect();

        let aux_preds_cat = Tensor::cat(aux_preds.clone(), 2); //[d, t, num_aux_labels]

        let primary_pred_final = self
            .linear_out
            .forward(aux_preds_cat.reshape([b * s, self.base_models.len()]));
        let primary_pred_final = primary_pred_final.reshape([b, s, 1]);

        let primary_pred_direct = self.base_models[0].forward(x);

        let mut all_tensor = vec![primary_pred_direct];
        all_tensor.append(&mut aux_preds);
        let all_direct_preds: Tensor<B, 3> = Tensor::cat(all_tensor, 2);

        (primary_pred_final, all_direct_preds)
    }
}
