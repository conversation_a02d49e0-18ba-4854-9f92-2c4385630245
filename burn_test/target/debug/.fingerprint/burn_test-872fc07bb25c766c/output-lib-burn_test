{"$message_type":"diagnostic","message":"unused imports: `MnistBatcher` and `ModelConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":79,"byte_end":91,"line_start":2,"line_end":2,"column_start":67,"column_end":79,"is_primary":true,"text":[{"text":"    data::{<PERSON><PERSON>atch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},","highlight_start":67,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":113,"byte_end":124,"line_start":3,"line_end":3,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"    model::{<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ModelRConfig},","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":77,"byte_end":91,"line_start":2,"line_end":2,"column_start":65,"column_end":79,"is_primary":true,"text":[{"text":"    data::{FactorBatch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},","highlight_start":65,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":111,"byte_end":124,"line_start":3,"line_end":3,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    model::{Model, ModelConfig, ModelR, ModelRConfig},","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `MnistBatcher` and `ModelConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:2:67\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    data::{FactorBatch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    model::{Model, ModelConfig, ModelR, ModelRConfig},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AccuracyMetric` and `dataset::vision::MnistDataset`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":206,"byte_end":235,"line_start":6,"line_end":6,"column_start":43,"column_end":72,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":43,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":533,"byte_end":547,"line_start":14,"line_end":14,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":204,"byte_end":235,"line_start":6,"line_end":6,"column_start":41,"column_end":72,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":41,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":174,"byte_end":175,"line_start":6,"line_end":6,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":235,"byte_end":236,"line_start":6,"line_end":6,"column_start":72,"column_end":73,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":72,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":533,"byte_end":549,"line_start":14,"line_end":14,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":532,"byte_end":533,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":559,"byte_end":560,"line_start":14,"line_end":14,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AccuracyMetric` and `dataset::vision::MnistDataset`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:6:43\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        metric::{AccuracyMetric, LossMetric},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ndarray`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":576,"byte_end":583,"line_start":17,"line_end":17,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"use ndarray;","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":572,"byte_end":585,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use ndarray;","highlight_start":1,"highlight_end":13},{"text":"use ndarray_npy;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ndarray`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse ndarray;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"3 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 3 warnings emitted\u001b[0m\n\n"}
