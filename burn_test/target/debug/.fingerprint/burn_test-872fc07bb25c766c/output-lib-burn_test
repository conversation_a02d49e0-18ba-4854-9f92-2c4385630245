{"$message_type":"diagnostic","message":"unused imports: `MnistBatcher` and `ModelConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":79,"byte_end":91,"line_start":2,"line_end":2,"column_start":67,"column_end":79,"is_primary":true,"text":[{"text":"    data::{<PERSON><PERSON>atch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},","highlight_start":67,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":113,"byte_end":124,"line_start":3,"line_end":3,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"    model::{<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ModelRConfig},","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":77,"byte_end":91,"line_start":2,"line_end":2,"column_start":65,"column_end":79,"is_primary":true,"text":[{"text":"    data::{FactorBatch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},","highlight_start":65,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":111,"byte_end":124,"line_start":3,"line_end":3,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    model::{Model, ModelConfig, ModelR, ModelRConfig},","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `MnistBatcher` and `ModelConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:2:67\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    data::{FactorBatch, FactorBatcher, FactorDataset, MnistBatch, MnistBatcher},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    model::{Model, ModelConfig, ModelR, ModelRConfig},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AccuracyMetric` and `dataset::vision::MnistDataset`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":206,"byte_end":235,"line_start":6,"line_end":6,"column_start":43,"column_end":72,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":43,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":533,"byte_end":547,"line_start":14,"line_end":14,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":204,"byte_end":235,"line_start":6,"line_end":6,"column_start":41,"column_end":72,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":41,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":174,"byte_end":175,"line_start":6,"line_end":6,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":235,"byte_end":236,"line_start":6,"line_end":6,"column_start":72,"column_end":73,"is_primary":true,"text":[{"text":"    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},","highlight_start":72,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":533,"byte_end":549,"line_start":14,"line_end":14,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":532,"byte_end":533,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/training.rs","byte_start":559,"byte_end":560,"line_start":14,"line_end":14,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"        metric::{AccuracyMetric, LossMetric},","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AccuracyMetric` and `dataset::vision::MnistDataset`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:6:43\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    data::{dataloader::DataLoaderBuilder, dataset::vision::MnistDataset},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        metric::{AccuracyMetric, LossMetric},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ndarray`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/training.rs","byte_start":576,"byte_end":583,"line_start":17,"line_end":17,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"use ndarray;","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/training.rs","byte_start":572,"byte_end":585,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use ndarray;","highlight_start":1,"highlight_end":13},{"text":"use ndarray_npy;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ndarray`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse ndarray;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this method are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/training.rs","byte_start":2064,"byte_end":2082,"line_start":63,"line_end":63,"column_start":25,"column_end":43,"is_primary":true,"text":[{"text":"        let item = self.forward_regression(batch.features, batch.labels);","highlight_start":25,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `3`, found `2`","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":2083,"byte_end":2097,"line_start":63,"line_end":63,"column_start":44,"column_end":58,"is_primary":true,"text":[{"text":"        let item = self.forward_regression(batch.features, batch.labels);","highlight_start":44,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected struct `burn::tensor::Tensor<_, 3>`\n   found struct `burn::tensor::Tensor<_, 2>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"expected `3`, found `2`","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":2099,"byte_end":2111,"line_start":63,"line_end":63,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"        let item = self.forward_regression(batch.features, batch.labels);","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected struct `burn::tensor::Tensor<_, 3>`\n   found struct `burn::tensor::Tensor<_, 2>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":1115,"byte_end":1130,"line_start":38,"line_end":38,"column_start":9,"column_end":24,"is_primary":false,"text":[{"text":"        x: Tensor<B, 3>,","highlight_start":9,"highlight_end":24}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":1140,"byte_end":1161,"line_start":39,"line_end":39,"column_start":9,"column_end":30,"is_primary":false,"text":[{"text":"        targets: Tensor<B, 3>,","highlight_start":9,"highlight_end":30}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":1072,"byte_end":1090,"line_start":36,"line_end":36,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn forward_regression(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: arguments to this method are incorrect\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:63:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let item = self.forward_regression(batch.features, batch.labels);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `3`, found `2`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:63:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let item = self.forward_regression(batch.features, batch.labels);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m3\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m              found struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m2\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `3`, found `2`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:63:60\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let item = self.forward_regression(batch.features, batch.labels);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m3\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m              found struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m2\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:36:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn forward_regression(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        x: Tensor<B, 3>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        targets: Tensor<B, 3>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"arguments to this method are incorrect","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/training.rs","byte_start":2343,"byte_end":2361,"line_start":71,"line_end":71,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"        self.forward_regression(batch.features, batch.labels)","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `3`, found `2`","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":2362,"byte_end":2376,"line_start":71,"line_end":71,"column_start":33,"column_end":47,"is_primary":true,"text":[{"text":"        self.forward_regression(batch.features, batch.labels)","highlight_start":33,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected struct `burn::tensor::Tensor<_, 3>`\n   found struct `burn::tensor::Tensor<_, 2>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"expected `3`, found `2`","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":2378,"byte_end":2390,"line_start":71,"line_end":71,"column_start":49,"column_end":61,"is_primary":true,"text":[{"text":"        self.forward_regression(batch.features, batch.labels)","highlight_start":49,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected struct `burn::tensor::Tensor<_, 3>`\n   found struct `burn::tensor::Tensor<_, 2>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src/training.rs","byte_start":1115,"byte_end":1130,"line_start":38,"line_end":38,"column_start":9,"column_end":24,"is_primary":false,"text":[{"text":"        x: Tensor<B, 3>,","highlight_start":9,"highlight_end":24}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":1140,"byte_end":1161,"line_start":39,"line_end":39,"column_start":9,"column_end":30,"is_primary":false,"text":[{"text":"        targets: Tensor<B, 3>,","highlight_start":9,"highlight_end":30}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/training.rs","byte_start":1072,"byte_end":1090,"line_start":36,"line_end":36,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn forward_regression(","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: arguments to this method are incorrect\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:71:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.forward_regression(batch.features, batch.labels)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `3`, found `2`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:71:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.forward_regression(batch.features, batch.labels)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m3\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m              found struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m2\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `3`, found `2`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:71:49\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.forward_regression(batch.features, batch.labels)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m3\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m              found struct `burn::tensor::Tensor<_, \u001b[0m\u001b[0m\u001b[1m\u001b[35m2\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/training.rs:36:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn forward_regression(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        &self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        x: Tensor<B, 3>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        targets: Tensor<B, 3>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors; 3 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0308`.\u001b[0m\n"}
