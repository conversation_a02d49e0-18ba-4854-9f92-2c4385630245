{"rustc": 11410426090777951712, "features": "[\"burn-autodiff\", \"bytemuck\", \"default\", \"itertools\", \"macerator\", \"seq-macro\", \"simd\", \"std\"]", "declared_features": "[\"blas-accelerate\", \"blas-netlib\", \"blas-openblas\", \"blas-openblas-system\", \"blas-src\", \"burn-autodiff\", \"bytemuck\", \"default\", \"doc\", \"itertools\", \"macerator\", \"openblas-src\", \"seq-macro\", \"simd\", \"std\"]", "target": 6225654044190763116, "profile": 2040997289075261528, "path": 15319339394021538860, "deps": [[165552523706483759, "atomic_float", false, 3123602082721948788], [1768035099675089977, "derive_new", false, 7304470188533304933], [2705670324392674662, "burn_autodiff", false, 5382100505028621609], [3670781344591879731, "macerator", false, 9884004146617450278], [5157631553186200874, "num_traits", false, 16444761138488160619], [6643739152182419278, "bytemuck", false, 13717552110460458512], [10012205734978813886, "libm", false, 15073395811708907293], [11916940916964035392, "rand", false, 12483501903551331466], [12405711135109940338, "seq_macro", false, 6551688329877011799], [14315634199301950082, "burn_common", false, 16984027794163343727], [15383637073087263990, "burn_ir", false, 18118613756401416483], [15816094149100266419, "burn_tensor", false, 13453243465669570130], [15826188163127377936, "matrixmultiply", false, 6870051934227038661], [16326338539882746041, "itertools", false, 3920975927956779438], [16600341567479762689, "build_script_build", false, 9604027800768008554], [16912361351712441665, "spin", false, 13885561220330631940], [17605717126308396068, "paste", false, 15048037639290610914], [18259966568667970611, "n<PERSON><PERSON>", false, 5198931360931787997]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-ndarray-2da42920eeb9e831/dep-lib-burn_ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}