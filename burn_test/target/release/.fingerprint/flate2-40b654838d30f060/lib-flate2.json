{"rustc": 11410426090777951712, "features": "[\"any_impl\", \"any_zlib\", \"default\", \"libz-rs-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib-rs\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 2143351902565092882, "deps": [[5150019917558475664, "libz_rs_sys", false, 4419915912809422484], [7312356825837975969, "crc32fast", false, 13466373526448531235], [7636735136738807108, "miniz_oxide", false, 13358535743608499393]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-40b654838d30f060/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}