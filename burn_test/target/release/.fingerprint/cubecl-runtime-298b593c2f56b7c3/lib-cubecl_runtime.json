{"rustc": 11410426090777951712, "features": "[\"channel-cell\", \"channel-mpsc\", \"channel-mutex\", \"default\", \"dirs\", \"std\", \"storage-bytes\", \"toml\"]", "declared_features": "[\"autotune-checks\", \"channel-cell\", \"channel-mpsc\", \"channel-mutex\", \"default\", \"dirs\", \"exclusive-memory-only\", \"profile-tracy\", \"std\", \"storage-bytes\", \"toml\"]", "target": 8299208286389666395, "profile": 2040997289075261528, "path": 9953029720615935357, "deps": [[1326543258065490303, "cubecl_common", false, 9145360164001417748], [1768035099675089977, "derive_new", false, 7304470188533304933], [2122527694408733004, "variadics_please", false, 10061990595136711760], [2828590642173593838, "cfg_if", false, 14455516465753162616], [5986029879202738730, "log", false, 14168601334365909054], [6633419628244209595, "async_channel", false, 17960551031562210458], [6643739152182419278, "bytemuck", false, 13717552110460458512], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9689903380558560274, "serde", false, 12118504321310545892], [10195042395026127797, "cubecl_ir", false, 15337807215381531288], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 16747950232832493091], [12060164242600251039, "toml", false, 11189865599543851272], [16362055519698394275, "serde_json", false, 3604051353282888081], [16912361351712441665, "spin", false, 13885561220330631940], [16928111194414003569, "dirs", false, 6389749896276399924], [17010343915181501705, "build_script_build", false, 2667071949078957954], [17548070434322971711, "md5", false, 8298394353706412924]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-runtime-298b593c2f56b7c3/dep-lib-cubecl_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}