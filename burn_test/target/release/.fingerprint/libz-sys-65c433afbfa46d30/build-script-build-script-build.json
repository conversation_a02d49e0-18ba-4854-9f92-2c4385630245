{"rustc": 11410426090777951712, "features": "[\"libc\", \"static\"]", "declared_features": "[\"asm\", \"cmake\", \"default\", \"libc\", \"static\", \"stock-zlib\", \"zlib-ng\", \"zlib-ng-no-cmake-experimental-community-maintained\"]", "target": 17883862002600103897, "profile": 15979580147010839785, "path": 16335919161764248438, "deps": [[3214373357989284387, "pkg_config", false, 9153466618375497657], [12933202132622624734, "vcpkg", false, 7479770919014888990], [17145785497280625295, "cc", false, 13006544226240326284]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libz-sys-65c433afbfa46d30/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}