{"rustc": 11410426090777951712, "features": "[\"default\", \"std\"]", "declared_features": "[\"ash\", \"conv_tests\", \"cubecl-cpp\", \"cubecl-spirv\", \"default\", \"exclusive-memory-only\", \"matmul_tests_all\", \"matmul_tests_alt_shapes\", \"matmul_tests_barrier\", \"matmul_tests_base\", \"matmul_tests_cyclic\", \"matmul_tests_double\", \"matmul_tests_f16\", \"matmul_tests_f32\", \"matmul_tests_hybrid\", \"matmul_tests_hypercube\", \"matmul_tests_layouts\", \"matmul_tests_ordered\", \"matmul_tests_partition_buffering\", \"matmul_tests_plane\", \"matmul_tests_simple\", \"matmul_tests_specialized\", \"matmul_tests_strided\", \"matmul_tests_tilewise\", \"matmul_tests_tma\", \"matmul_tests_unit\", \"msl\", \"profile-tracy\", \"sanitize-filename\", \"spirv\", \"spirv-dump\", \"std\", \"tracy-client\"]", "target": 16407437557763303198, "profile": 2040997289075261528, "path": 13602581876220445186, "deps": [[1326543258065490303, "cubecl_common", false, 9145360164001417748], [1768035099675089977, "derive_new", false, 7304470188533304933], [2828590642173593838, "cfg_if", false, 14455516465753162616], [5986029879202738730, "log", false, 14168601334365909054], [6633419628244209595, "async_channel", false, 17960551031562210458], [6643739152182419278, "bytemuck", false, 13717552110460458512], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9413735543017745434, "wgpu", false, 5233439493784418240], [11293676373856528358, "derive_more", false, 8575003771282198524], [12263923392487481371, "cubecl_core", false, 11894800734805942146], [13818794756669467285, "build_script_build", false, 6861292124307183137], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-wgpu-97699357fe014f2b/dep-lib-cubecl_wgpu", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}