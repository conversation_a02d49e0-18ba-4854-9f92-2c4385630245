{"rustc": 11410426090777951712, "features": "[\"default\", \"gzip\", \"std\", \"zlib\"]", "declared_features": "[\"default\", \"gzip\", \"nightly\", \"std\", \"zlib\"]", "target": 10788940031695432753, "profile": 2040997289075261528, "path": 16571166841455656377, "deps": [[4018467389006652250, "simd_adler32", false, 11869246208060650273], [5986029879202738730, "log", false, 14168601334365909054], [7312356825837975969, "crc32fast", false, 13466373526448531235], [13336078982182647123, "<PERSON><PERSON>", false, 5922704029952549002]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zopfli-d2f7a0c7a6b1d43c/dep-lib-zopfli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}