{"rustc": 11410426090777951712, "features": "[\"arrays\", \"experimental\", \"legacy\", \"std\", \"zdict_builder\", \"zstdmt\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 15794574819684997721, "path": 7294734171883865298, "deps": [[8373447648276846408, "zstd_sys", false, 17612734542892771621], [15788444815745660356, "build_script_build", false, 1348238765306001412]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-safe-cdafb55cb9cde6c8/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}