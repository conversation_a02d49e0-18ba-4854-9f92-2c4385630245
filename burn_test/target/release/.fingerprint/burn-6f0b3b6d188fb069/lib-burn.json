{"rustc": 11410426090777951712, "features": "[\"autodiff\", \"burn-autodiff\", \"burn-cuda\", \"burn-ir\", \"burn-train\", \"burn-wgpu\", \"cuda\", \"dataset\", \"fusion\", \"ir\", \"std\", \"train\", \"tui\", \"vision\", \"wgpu\"]", "declared_features": "[\"accelerate\", \"audio\", \"autodiff\", \"autotune\", \"autotune-checks\", \"blas-netlib\", \"burn-autodiff\", \"burn-candle\", \"burn-cuda\", \"burn-ir\", \"burn-ndarray\", \"burn-remote\", \"burn-rocm\", \"burn-router\", \"burn-tch\", \"burn-train\", \"burn-wgpu\", \"candle\", \"candle-cuda\", \"candle-metal\", \"compilation-cache\", \"cuda\", \"dataset\", \"default\", \"doc\", \"experimental-named-tensor\", \"fusion\", \"ir\", \"metal\", \"metrics\", \"ndarray\", \"network\", \"openblas\", \"openblas-system\", \"record-item-custom-serde\", \"remote\", \"rocm\", \"router\", \"server\", \"simd\", \"sqlite\", \"sqlite-bundled\", \"std\", \"tch\", \"template\", \"train\", \"tui\", \"vision\", \"vulkan\", \"webgpu\", \"wgpu\"]", "target": 9777084416644796484, "profile": 2040997289075261528, "path": 5702370910960903473, "deps": [[1346835753664595952, "burn_wgpu", false, 4571628396404521455], [2705670324392674662, "burn_autodiff", false, 5382100505028621609], [6891896781140757826, "burn_cuda", false, 7227618548788439043], [15383637073087263990, "burn_ir", false, 18118613756401416483], [16323708659398809212, "burn_train", false, 13221295755947351794], [17691736867478172063, "burn_core", false, 16811721419829376127]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-6f0b3b6d188fb069/dep-lib-burn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}