{"rustc": 11410426090777951712, "features": "[\"std\"]", "declared_features": "[\"default\", \"export-internal\", \"grammar-extras\", \"not-bootstrap-in-src\", \"std\"]", "target": 3031267579843285925, "profile": 1369601567987815722, "path": 4445848500069047421, "deps": [[737309000288863258, "proc_macro2", false, 8639221084210752112], [3221585212778410572, "pest", false, 12741154226763108261], [3395339557636834855, "pest_meta", false, 10604657734466388369], [4974441333307933176, "syn", false, 18101083334617769417], [17990358020177143287, "quote", false, 8619764752310940647]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/pest_generator-f6b4905caf689c45/dep-lib-pest_generator", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}