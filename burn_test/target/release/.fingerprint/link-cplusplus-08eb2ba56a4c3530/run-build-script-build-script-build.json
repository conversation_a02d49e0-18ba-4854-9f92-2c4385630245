{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17256083728732019893, "build_script_build", false, 5192234429127599418]], "local": [{"RerunIfChanged": {"output": "release/build/link-cplusplus-08eb2ba56a4c3530/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}