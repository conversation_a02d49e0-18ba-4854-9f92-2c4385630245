{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2040997289075261528, "path": 4269474282906693760, "deps": [[2828590642173593838, "cfg_if", false, 14455516465753162616], [5491919304041016563, "build_script_build", false, 18225216075309152441], [8995469080876806959, "untrusted", false, 10887719999089759184], [9920160576179037441, "getrandom", false, 13503210491210646117]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ring-3a346aef591f74ff/dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}