{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17022423707615322322, "build_script_build", false, 5393296916828229863]], "local": [{"RerunIfChanged": {"output": "release/build/libz-sys-6001841b1a0a9ee7/output", "paths": ["build.rs", "zng/cmake.rs", "zng/cc.rs"]}}, {"RerunIfEnvChanged": {"var": "LIBZ_SYS_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-gnu", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}