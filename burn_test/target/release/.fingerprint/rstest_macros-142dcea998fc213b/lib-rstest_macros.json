{"rustc": 11410426090777951712, "features": "[\"async-timeout\", \"crate-name\"]", "declared_features": "[\"async-timeout\", \"crate-name\", \"default\"]", "target": 16651690207514469086, "profile": 1369601567987815722, "path": 17169607895107116420, "deps": [[737309000288863258, "proc_macro2", false, 8639221084210752112], [1001858567591969688, "build_script_build", false, 4592455992334999778], [1988483478007900009, "unicode_ident", false, 16663740234822330835], [2828590642173593838, "cfg_if", false, 8264099263296051426], [4974441333307933176, "syn", false, 18101083334617769417], [8978325847672661817, "relative_path", false, 17548175085466720044], [9451456094439810778, "regex", false, 4507658003578847541], [15203748914246919255, "proc_macro_crate", false, 2743096682336978649], [17155886227862585100, "glob", false, 2658690076830812816], [17990358020177143287, "quote", false, 8619764752310940647]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rstest_macros-142dcea998fc213b/dep-lib-rstest_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}