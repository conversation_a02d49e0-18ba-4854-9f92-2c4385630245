{"rustc": 11410426090777951712, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"export_tests\", \"std\", \"template\"]", "target": 18403291948739383805, "profile": 2040997289075261528, "path": 13441187091000508108, "deps": [[850240812968748911, "cubecl_macros", false, 2726250243619947756], [1326543258065490303, "cubecl_common", false, 9145360164001417748], [1768035099675089977, "derive_new", false, 7304470188533304933], [2122527694408733004, "variadics_please", false, 10061990595136711760], [5157631553186200874, "num_traits", false, 16444761138488160619], [5986029879202738730, "log", false, 14168601334365909054], [6643739152182419278, "bytemuck", false, 13717552110460458512], [7896293946984509699, "bitflags", false, 6188247286135038649], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9689903380558560274, "serde", false, 12118504321310545892], [10195042395026127797, "cubecl_ir", false, 15337807215381531288], [11293676373856528358, "derive_more", false, 8575003771282198524], [16362055519698394275, "serde_json", false, 3604051353282888081], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841], [17605717126308396068, "paste", false, 15048037639290610914]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-core-dd44f013f06e02b9/dep-lib-cubecl_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}