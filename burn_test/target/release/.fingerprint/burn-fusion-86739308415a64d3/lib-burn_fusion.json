{"rustc": 11410426090777951712, "features": "[\"default\", \"std\"]", "declared_features": "[\"default\", \"doc\", \"memory-checks\", \"std\"]", "target": 14388388406921666634, "profile": 2040997289075261528, "path": 16268947529586268499, "deps": [[1768035099675089977, "derive_new", false, 7304470188533304933], [5986029879202738730, "log", false, 14168601334365909054], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9689903380558560274, "serde", false, 12118504321310545892], [14315634199301950082, "burn_common", false, 16984027794163343727], [15383637073087263990, "burn_ir", false, 18118613756401416483], [15816094149100266419, "burn_tensor", false, 13453243465669570130], [16857843618210199216, "half", false, 8502051731271364958], [16912361351712441665, "spin", false, 13885561220330631940]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-fusion-86739308415a64d3/dep-lib-burn_fusion", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}