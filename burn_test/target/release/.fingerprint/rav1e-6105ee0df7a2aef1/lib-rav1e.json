{"rustc": 11410426090777951712, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2040997289075261528, "path": 14923064392901099155, "deps": [[2687729594444538932, "debug_unreachable", false, 169442733713666688], [2828590642173593838, "cfg_if", false, 14455516465753162616], [3722963349756955755, "once_cell", false, 14176315018075933189], [4684437522915235464, "libc", false, 10405082303599363], [5157631553186200874, "num_traits", false, 16444761138488160619], [5237962722597217121, "simd_helpers", false, 8583503147763859830], [5626665093607998638, "build_script_build", false, 9158193431009977664], [5986029879202738730, "log", false, 14168601334365909054], [6697151524989202978, "profiling", false, 6505254618109996319], [7074416887430417773, "av1_grain", false, 6397398557720380903], [8008191657135824715, "thiserror", false, 7211105242224955984], [11263754829263059703, "num_derive", false, 16895938336266570833], [12672448913558545127, "noop_proc_macro", false, 3276890227691312084], [13847662864258534762, "arrayvec", false, 4436612595913357663], [14931062873021150766, "itertools", false, 11491398222767828241], [15325537792103828505, "v_frame", false, 15831767558568181718], [16507960196461048755, "rayon", false, 8518505245116460357], [17605717126308396068, "paste", false, 15048037639290610914], [17706129463675219700, "arg_enum_proc_macro", false, 16373252303457909588], [17933778289016427379, "bitstream_io", false, 9398253454698431592]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rav1e-6105ee0df7a2aef1/dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}