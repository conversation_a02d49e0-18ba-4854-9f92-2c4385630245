{"rustc": 11410426090777951712, "features": "[\"compact\", \"default\", \"glsl-out\", \"hlsl-out\", \"spv-out\", \"wgsl-in\"]", "declared_features": "[\"arbitrary\", \"compact\", \"default\", \"deserialize\", \"dot-out\", \"glsl-in\", \"glsl-out\", \"hlsl-out\", \"hlsl-out-if-target-windows\", \"msl-out\", \"msl-out-if-target-apple\", \"serialize\", \"spv-in\", \"spv-out\", \"wgsl-in\", \"wgsl-out\"]", "target": 4515025569811112816, "profile": 5120242099289791056, "path": 14162337605252847656, "deps": [[1988483478007900009, "unicode_ident", false, 15753910149498314220], [3302295501534065768, "strum", false, 10508797874449834472], [3722963349756955755, "once_cell", false, 14176315018075933189], [5157631553186200874, "num_traits", false, 16444761138488160619], [5986029879202738730, "log", false, 14168601334365909054], [6493259146304816786, "indexmap", false, 15063877505773308730], [7087646484460728752, "spirv", false, 4120514171817736024], [7159903911403017211, "codespan_reporting", false, 3903422756819808281], [7896293946984509699, "bitflags", false, 6188247286135038649], [8588622563673530609, "build_script_build", false, 2205930156275531446], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9519969280819313548, "bit_set", false, 13924023170882207066], [10806645703491011684, "thiserror", false, 15518112415476572836], [11741667666137467643, "hexf_parse", false, 5814946281188596712], [13847662864258534762, "arrayvec", false, 4436612595913357663], [16055916053474393816, "rustc_hash", false, 9475526548128589797], [16857843618210199216, "half", false, 8502051731271364958]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/naga-6af316727a0c7246/dep-lib-naga", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}