{"rustc": 11410426090777951712, "features": "[\"floats\", \"integers\", \"lexical-parse-float\", \"lexical-parse-integer\", \"lexical-write-float\", \"lexical-write-integer\", \"parse\", \"parse-floats\", \"parse-integers\", \"write\", \"write-floats\", \"write-integers\"]", "declared_features": "[\"compact\", \"default\", \"f128\", \"f16\", \"floats\", \"format\", \"integers\", \"lexical-parse-float\", \"lexical-parse-integer\", \"lexical-write-float\", \"lexical-write-integer\", \"lint\", \"parse\", \"parse-floats\", \"parse-integers\", \"power-of-two\", \"radix\", \"std\", \"write\", \"write-floats\", \"write-integers\"]", "target": 5789473667312678097, "profile": 2040997289075261528, "path": 14839449080930414043, "deps": [[757373295804312759, "lexical_parse_float", false, 4240585681800132699], [3800171689049377331, "lexical_util", false, 16927589495127474210], [5207363620914184311, "lexical_write_integer", false, 16025795919553338621], [15474330194059950047, "lexical_parse_integer", false, 5325123652348537186], [17217489311556736288, "lexical_write_float", false, 5750459668719285079]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/lexical-core-431d597bba039e88/dep-lib-lexical_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}