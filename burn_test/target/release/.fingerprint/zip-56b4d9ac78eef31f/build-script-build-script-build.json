{"rustc": 11410426090777951712, "features": "[\"_deflate-any\", \"deflate\", \"deflate-flate2\", \"deflate-zopfli\", \"flate2\", \"zopfli\"]", "declared_features": "[\"_all-features\", \"_deflate-any\", \"aes\", \"aes-crypto\", \"bzip2\", \"chrono\", \"constant_time_eq\", \"default\", \"deflate\", \"deflate-flate2\", \"deflate-miniz\", \"deflate-zlib\", \"deflate-zlib-ng\", \"deflate-zopfli\", \"deflate64\", \"flate2\", \"getrandom\", \"hmac\", \"lzma\", \"lzma-rs\", \"nt-time\", \"pbkdf2\", \"sha1\", \"time\", \"unreserved\", \"xz\", \"zeroize\", \"zopfli\", \"zstd\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 9934565833983842891, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zip-56b4d9ac78eef31f/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}