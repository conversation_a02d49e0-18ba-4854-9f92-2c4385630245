{"rustc": 11410426090777951712, "features": "[\"add\", \"add_assign\", \"default\", \"deref\", \"display\", \"from\", \"into\", \"mul\", \"mul_assign\", \"not\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 3601279031184244129, "path": 17622455621976307284, "deps": [[737309000288863258, "proc_macro2", false, 8639221084210752112], [4974441333307933176, "syn", false, 18101083334617769417], [16126285161989458480, "unicode_xid", false, 11171009542492431452], [17990358020177143287, "quote", false, 8619764752310940647]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/derive_more-impl-4cd9caaa3fbbb77c/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}