{"rustc": 11410426090777951712, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 427045877367790886, "deps": [[1542112352204983347, "build_script_build", false, 7830323384227093019], [2883436298747778685, "pki_types", false, 1845782261448335985], [3722963349756955755, "once_cell", false, 14176315018075933189], [5491919304041016563, "ring", false, 4020048937042323252], [6528079939221783635, "zeroize", false, 6412979175749591779], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 15365003133284564018], [17003143334332120809, "subtle", false, 17191717731325008666]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustls-50e9e8532cd050f9/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}