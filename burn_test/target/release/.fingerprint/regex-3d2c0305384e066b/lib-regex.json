{"rustc": 11410426090777951712, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 1369601567987815722, "path": 10201376523292356109, "deps": [[555019317135488525, "regex_automata", false, 659239575453836597], [2779309023524819297, "aho_corasick", false, 12323461249586367618], [9408802513701742484, "regex_syntax", false, 12985265924217657929], [15932120279885307830, "memchr", false, 3109086684637857395]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-3d2c0305384e066b/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}