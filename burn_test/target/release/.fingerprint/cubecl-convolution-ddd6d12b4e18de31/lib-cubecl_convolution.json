{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"conv_tests\", \"default\", \"export_tests\", \"pretty_assertions\", \"std\"]", "target": 483257230503941215, "profile": 2040997289075261528, "path": 3838425789873124900, "deps": [[1326543258065490303, "cubecl_common", false, 9145360164001417748], [6643739152182419278, "bytemuck", false, 13717552110460458512], [7897962569138937875, "cubecl_std", false, 6109247288786872331], [9689903380558560274, "serde", false, 12118504321310545892], [10761050467557221258, "cubecl_random", false, 16814099126558485132], [12263923392487481371, "cubecl_core", false, 11894800734805942146], [12809737404969064906, "cubecl_reduce", false, 10550623510448321310], [14807510782550627175, "cubecl_matmul", false, 15757484911038938359], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-convolution-ddd6d12b4e18de31/dep-lib-cubecl_convolution", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}