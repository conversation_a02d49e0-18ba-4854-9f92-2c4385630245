{"rustc": 11410426090777951712, "features": "[\"add\", \"add_assign\", \"display\", \"mul\", \"mul_assign\", \"not\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 3601279031184244129, "path": 4461055623238068411, "deps": [[15774985133158646067, "derive_more_impl", false, 8724028100510173319]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/derive_more-6b46635d3d3b48ed/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}