{"rustc": 11410426090777951712, "features": "[\"colored\", \"cubecl\", \"cubecl-cuda\", \"cubecl-wgpu\", \"default\", \"std\"]", "declared_features": "[\"burn-tensor-testgen\", \"colored\", \"cubecl\", \"cubecl-cuda\", \"cubecl-hip\", \"cubecl-wgpu\", \"default\", \"doc\", \"experimental-named-tensor\", \"export_tests\", \"std\"]", "target": 17861724778555176769, "profile": 2040997289075261528, "path": 228816764648372149, "deps": [[1768035099675089977, "derive_new", false, 7304470188533304933], [5157631553186200874, "num_traits", false, 16444761138488160619], [6643739152182419278, "bytemuck", false, 13717552110460458512], [8580214316202659832, "cubecl", false, 9414100189597328283], [8921336173939679069, "hashbrown", false, 2780228547192054506], [9114801806035203750, "colored", false, 17135027776915889521], [9689903380558560274, "serde", false, 12118504321310545892], [10620374951647533631, "rand_distr", false, 15109294420986805363], [10889494155287625682, "serde_bytes", false, 2638821342795528528], [11916940916964035392, "rand", false, 12483501903551331466], [14315634199301950082, "burn_common", false, 16984027794163343727], [16857843618210199216, "half", false, 8502051731271364958]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-tensor-7c01fd2c6558a26a/dep-lib-burn_tensor", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}