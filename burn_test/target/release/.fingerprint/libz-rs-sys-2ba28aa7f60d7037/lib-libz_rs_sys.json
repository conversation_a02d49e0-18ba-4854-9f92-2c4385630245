{"rustc": 11410426090777951712, "features": "[\"rust-allocator\", \"std\"]", "declared_features": "[\"c-allocator\", \"custom-prefix\", \"default\", \"export-symbols\", \"rust-allocator\", \"semver-prefix\", \"std\", \"testing-prefix\"]", "target": 1934612030275886076, "profile": 2040997289075261528, "path": 11828401780178858272, "deps": [[17270865290439125615, "zlib_rs", false, 6507751327370279678]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libz-rs-sys-2ba28aa7f60d7037/dep-lib-libz_rs_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}