{"rustc": 11410426090777951712, "features": "[\"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"arbitrary\", \"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 742186494246220192, "profile": 2040997289075261528, "path": 10751334265423225387, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-syntax-bf46e03b2b2875b5/dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}