{"rustc": 11410426090777951712, "features": "[\"std\"]", "declared_features": "[\"async\", \"burn-tensor-testgen\", \"default\", \"export_tests\", \"std\"]", "target": 6162918904188656467, "profile": 2040997289075261528, "path": 619255707264694046, "deps": [[1768035099675089977, "derive_new", false, 7304470188533304933], [3958489542916937055, "portable_atomic", false, 12545429305141747150], [5157631553186200874, "num_traits", false, 16444761138488160619], [5986029879202738730, "log", false, 14168601334365909054], [8921336173939679069, "hashbrown", false, 2780228547192054506], [14315634199301950082, "burn_common", false, 16984027794163343727], [15816094149100266419, "burn_tensor", false, 13453243465669570130], [16912361351712441665, "spin", false, 13885561220330631940]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-autodiff-429d0abd2e911f11/dep-lib-burn_autodiff", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}