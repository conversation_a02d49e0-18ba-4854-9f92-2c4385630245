{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"debug_symbols\", \"default\", \"nightly\", \"std\"]", "target": 13042819591254046283, "profile": 1369601567987815722, "path": 12741757973936184654, "deps": [[737309000288863258, "proc_macro2", false, 8639221084210752112], [850240812968748911, "build_script_build", false, 14782865558308308288], [1326543258065490303, "cubecl_common", false, 700304589039749121], [1696678251960462246, "prettyplease", false, 18182872804646254814], [1768035099675089977, "derive_new", false, 7304470188533304933], [2827826115502691620, "darling", false, 2863874393127707412], [4974441333307933176, "syn", false, 18101083334617769417], [15383437925411509181, "ident_case", false, 8712296799619901361], [17990358020177143287, "quote", false, 8619764752310940647]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-macros-29011e55d181a917/dep-lib-cubecl_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}