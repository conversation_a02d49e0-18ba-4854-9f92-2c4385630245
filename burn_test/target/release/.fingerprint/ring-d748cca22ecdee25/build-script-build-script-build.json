{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 11618217121801252310, "deps": [[17145785497280625295, "cc", false, 13006544226240326284]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ring-d748cca22ecdee25/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}