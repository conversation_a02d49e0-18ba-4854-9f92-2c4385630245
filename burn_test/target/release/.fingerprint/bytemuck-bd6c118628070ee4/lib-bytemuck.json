{"rustc": 11410426090777951712, "features": "[\"aarch64_simd\", \"avx512_simd\", \"bytemuck_derive\", \"derive\", \"extern_crate_alloc\", \"min_const_generics\", \"must_cast\", \"wasm_simd\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"impl_core_error\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\", \"zeroable_unwind_fn\"]", "target": 5195934831136530909, "profile": 15449492369883331296, "path": 13184476005013481474, "deps": [[9547749918651864678, "bytemuck_derive", false, 1732489106453363611]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bytemuck-bd6c118628070ee4/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}