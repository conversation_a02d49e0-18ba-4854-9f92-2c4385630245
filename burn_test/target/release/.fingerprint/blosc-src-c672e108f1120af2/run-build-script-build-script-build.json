{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16726347008276381358, "build_script_build", false, 1987169434493214992], [17022423707615322322, "build_script_build", false, 585484851592626055], [2012735159211117644, "build_script_build", false, 9659698575263773048], [3392622234319529730, "build_script_build", false, 1057741493479976400], [8373447648276846408, "build_script_build", false, 15396510936129765569]], "local": [{"RerunIfChanged": {"output": "release/build/blosc-src-c672e108f1120af2/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-unknown-linux-gnu", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}