{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"default\", \"export_tests\", \"pretty_assertions\", \"rand\", \"std\"]", "target": 17375253764984743944, "profile": 2040997289075261528, "path": 14821221372851385173, "deps": [[5157631553186200874, "num_traits", false, 16444761138488160619], [7897962569138937875, "cubecl_std", false, 6109247288786872331], [9689903380558560274, "serde", false, 12118504321310545892], [12263923392487481371, "cubecl_core", false, 11894800734805942146], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-reduce-4de557e727769647/dep-lib-cubecl_reduce", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}