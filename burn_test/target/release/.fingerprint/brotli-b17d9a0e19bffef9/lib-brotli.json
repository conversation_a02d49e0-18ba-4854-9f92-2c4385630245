{"rustc": 11410426090777951712, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 2040997289075261528, "path": 14738326892953847518, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 8441735591938577710], [16413620717702030930, "brotli_decompressor", false, 5320370488364830153], [17470296833448545982, "alloc_stdlib", false, 6170496436485283290]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/brotli-b17d9a0e19bffef9/dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}