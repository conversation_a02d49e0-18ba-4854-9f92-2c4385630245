{"rustc": 11410426090777951712, "features": "[\"burn-fusion\", \"fusion\", \"std\"]", "declared_features": "[\"autotune\", \"autotune-checks\", \"burn-fusion\", \"compilation-cache\", \"default\", \"doc\", \"fusion\", \"std\"]", "target": 1801460288307805235, "profile": 2040997289075261528, "path": 17649569236900205838, "deps": [[1768035099675089977, "derive_new", false, 7304470188533304933], [5986029879202738730, "log", false, 14168601334365909054], [6643739152182419278, "bytemuck", false, 13717552110460458512], [6831037983928876433, "burn_cubecl", false, 15506720306192215888], [8580214316202659832, "cubecl", false, 9414100189597328283], [13483217290581347012, "burn_fusion", false, 16463680667139470974], [15816094149100266419, "burn_tensor", false, 13453243465669570130], [16857843618210199216, "half", false, 8502051731271364958]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/burn-cuda-aef16d99f1e1a259/dep-lib-burn_cuda", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}