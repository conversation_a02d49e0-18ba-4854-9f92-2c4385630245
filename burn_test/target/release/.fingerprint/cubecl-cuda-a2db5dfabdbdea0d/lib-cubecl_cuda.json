{"rustc": 11410426090777951712, "features": "[\"default\", \"std\"]", "declared_features": "[\"compilation-cache\", \"conv_tests\", \"default\", \"matmul_tests_all\", \"matmul_tests_alt_shapes\", \"matmul_tests_barrier\", \"matmul_tests_base\", \"matmul_tests_cyclic\", \"matmul_tests_double\", \"matmul_tests_f16\", \"matmul_tests_f32\", \"matmul_tests_hybrid\", \"matmul_tests_hypercube\", \"matmul_tests_layouts\", \"matmul_tests_ordered\", \"matmul_tests_partition_buffering\", \"matmul_tests_plane\", \"matmul_tests_simple\", \"matmul_tests_specialized\", \"matmul_tests_strided\", \"matmul_tests_tilewise\", \"matmul_tests_tma\", \"matmul_tests_unit\", \"ptx-wmma\", \"std\"]", "target": 5059746603442045082, "profile": 2040997289075261528, "path": 11497599949257881376, "deps": [[1326543258065490303, "cubecl_common", false, 9145360164001417748], [1768035099675089977, "derive_new", false, 7304470188533304933], [5986029879202738730, "log", false, 14168601334365909054], [6643739152182419278, "bytemuck", false, 13717552110460458512], [9689903380558560274, "serde", false, 12118504321310545892], [10744180113354673229, "cudarc", false, 12327562549138119501], [12263923392487481371, "cubecl_core", false, 11894800734805942146], [15652778066443466134, "cubecl_cpp", false, 13159392119880463315], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-cuda-a2db5dfabdbdea0d/dep-lib-cubecl_cuda", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}