{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"comfy-table\", \"force_validate\", \"prettyprint\"]", "target": 8704346790006725872, "profile": 2040997289075261528, "path": 3464870904343628435, "deps": [[1216309103264968120, "ryu", false, 9490744865203253774], [2767651414059569738, "arrow_schema", false, 13517546583892486507], [4113097421141538081, "arrow_buffer", false, 5500815346452043256], [6460763250893544872, "arrow_data", false, 18142180966670359879], [9897246384292347999, "chrono", false, 1935600823659238272], [12905226474294026438, "num", false, 2893814878989160443], [13043324608583405220, "arrow_select", false, 11817077725137257541], [13077212702700853852, "base64", false, 9057814779437756187], [13149788140475665966, "lexical_core", false, 2623349821173749643], [14451838158078945517, "arrow_array", false, 11709807758118694834], [16857843618210199216, "half", false, 8502051731271364958], [17106256174509013259, "atoi", false, 12077745581151101537]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/arrow-cast-02c646c328c56d91/dep-lib-arrow_cast", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}