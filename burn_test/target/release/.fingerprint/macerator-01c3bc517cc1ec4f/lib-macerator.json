{"rustc": 11410426090777951712, "features": "[\"avx512\", \"default\", \"std\"]", "declared_features": "[\"avx512\", \"default\", \"fp16\", \"nightly\", \"std\"]", "target": 10277703881446905556, "profile": 2040997289075261528, "path": 3922081467598780566, "deps": [[3670781344591879731, "build_script_build", false, 4950534835397184740], [4246169283328986391, "macerator_macros", false, 17454071227601371150], [5157631553186200874, "num_traits", false, 16444761138488160619], [6643739152182419278, "bytemuck", false, 13717552110460458512], [15269934305406035263, "moddef", false, 8103617209943378185], [16857843618210199216, "half", false, 8502051731271364958], [17605717126308396068, "paste", false, 15048037639290610914]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/macerator-01c3bc517cc1ec4f/dep-lib-macerator", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}