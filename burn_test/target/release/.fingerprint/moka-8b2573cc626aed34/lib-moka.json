{"rustc": 11410426090777951712, "features": "[\"default\", \"sync\"]", "declared_features": "[\"async-lock\", \"atomic64\", \"default\", \"event-listener\", \"future\", \"futures-util\", \"log\", \"logging\", \"once_cell\", \"quanta\", \"sync\", \"unstable-debug-counters\"]", "target": 134841662324965077, "profile": 7804424307555407329, "path": 11119963135156435915, "deps": [[3215375941368511160, "tagptr", false, 9651492321772300623], [3528074118530651198, "crossbeam_epoch", false, 9525420537148867926], [3666196340704888985, "smallvec", false, 9246314239959020391], [3958489542916937055, "portable_atomic", false, 12545429305141747150], [4468123440088164316, "crossbeam_utils", false, 3315833469831483685], [4495526598637097934, "parking_lot", false, 99239981106309224], [8008191657135824715, "thiserror", false, 7211105242224955984], [8319709847752024821, "uuid", false, 2447919053684705811], [9727213718512686088, "crossbeam_channel", false, 1012962020567994988], [11433342109619500754, "build_script_build", false, 5566293571575485311]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/moka-8b2573cc626aed34/dep-lib-moka", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}