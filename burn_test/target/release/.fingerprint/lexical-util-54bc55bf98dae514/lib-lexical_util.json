{"rustc": 11410426090777951712, "features": "[\"floats\", \"integers\", \"parse\", \"parse-floats\", \"parse-integers\", \"write\", \"write-floats\", \"write-integers\"]", "declared_features": "[\"compact\", \"default\", \"f128\", \"f16\", \"floats\", \"format\", \"integers\", \"lint\", \"parse\", \"parse-floats\", \"parse-integers\", \"power-of-two\", \"radix\", \"std\", \"write\", \"write-floats\", \"write-integers\"]", "target": 2702788345393153884, "profile": 2040997289075261528, "path": 820610660877758261, "deps": [[13785866025199020095, "static_assertions", false, 5341368490919530982]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/lexical-util-54bc55bf98dae514/dep-lib-lexical_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}