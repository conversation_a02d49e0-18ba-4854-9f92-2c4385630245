{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"default\", \"export_tests\", \"matmul_tests_all\", \"matmul_tests_alt_shapes\", \"matmul_tests_barrier\", \"matmul_tests_base\", \"matmul_tests_cyclic\", \"matmul_tests_double\", \"matmul_tests_f16\", \"matmul_tests_f32\", \"matmul_tests_hybrid\", \"matmul_tests_hypercube\", \"matmul_tests_layouts\", \"matmul_tests_ordered\", \"matmul_tests_partition_buffering\", \"matmul_tests_plane\", \"matmul_tests_simple\", \"matmul_tests_specialized\", \"matmul_tests_strided\", \"matmul_tests_tilewise\", \"matmul_tests_tma\", \"matmul_tests_unit\", \"pretty_assertions\", \"std\"]", "target": 14163491742020432543, "profile": 2040997289075261528, "path": 9486482595366663760, "deps": [[1326543258065490303, "cubecl_common", false, 9145360164001417748], [6643739152182419278, "bytemuck", false, 13717552110460458512], [7897962569138937875, "cubecl_std", false, 6109247288786872331], [9689903380558560274, "serde", false, 12118504321310545892], [10761050467557221258, "cubecl_random", false, 16814099126558485132], [12263923392487481371, "cubecl_core", false, 11894800734805942146], [12809737404969064906, "cubecl_reduce", false, 10550623510448321310], [16857843618210199216, "half", false, 8502051731271364958], [17010343915181501705, "cubecl_runtime", false, 13821332569912799841]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-matmul-84c191b28b8b8f2a/dep-lib-cubecl_matmul", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}