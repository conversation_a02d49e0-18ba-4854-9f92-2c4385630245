{"rustc": 11410426090777951712, "features": "[\"default\", \"fragile-send-sync-non-atomic-wasm\", \"std\", \"thiserror\"]", "declared_features": "[\"counters\", \"default\", \"fragile-send-sync-non-atomic-wasm\", \"serde\", \"std\", \"strict_asserts\", \"thiserror\", \"trace\"]", "target": 9610300806511847649, "profile": 6755413729743531042, "path": 4276991378671671503, "deps": [[5986029879202738730, "log", false, 14168601334365909054], [6643739152182419278, "bytemuck", false, 13717552110460458512], [7896293946984509699, "bitflags", false, 6188247286135038649], [10806645703491011684, "thiserror", false, 15518112415476572836]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wgpu-types-d094c78dbe111d8e/dep-lib-wgpu_types", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}