{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"cache\", \"default\", \"dirs\", \"futures-lite\", \"sanitize-filename\", \"serde\", \"serde_json\", \"std\"]", "target": 14155629533759436264, "profile": 1369601567987815722, "path": 10305072884536738417, "deps": [[1326543258065490303, "build_script_build", false, 3086133750328310915], [1768035099675089977, "derive_new", false, 7304470188533304933], [2828590642173593838, "cfg_if", false, 8264099263296051426], [5157631553186200874, "num_traits", false, 12005228763954013800], [5986029879202738730, "log", false, 5282147525032682889], [6643739152182419278, "bytemuck", false, 17093607964569068103], [8921336173939679069, "hashbrown", false, 8242380881323169395], [9689903380558560274, "serde", false, 17918605700127080337], [9692672211701898146, "web_time", false, 11756039185783944454], [11293676373856528358, "derive_more", false, 13340387691025106521], [11916940916964035392, "rand", false, 6770513109311215277], [16857843618210199216, "half", false, 14306223770910200754], [16892911738405188433, "embassy_futures", false, 4868483428572530862], [16912361351712441665, "spin", false, 8384171866246763755]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/cubecl-common-34d449cbea55b0a2/dep-lib-cubecl_common", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}